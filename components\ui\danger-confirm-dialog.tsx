'use client';

import * as React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';

interface DangerConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  consequences: string[];
  confirmText: string;
  onConfirm: () => void;
  actionLabel?: string;
}

export function DangerConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  consequences,
  confirmText,
  onConfirm,
  actionLabel = '确认执行'
}: DangerConfirmDialogProps) {
  const [inputValue, setInputValue] = React.useState('');
  const [isValid, setIsValid] = React.useState(false);

  React.useEffect(() => {
    setIsValid(inputValue === confirmText);
  }, [inputValue, confirmText]);

  React.useEffect(() => {
    if (!open) {
      setInputValue('');
      setIsValid(false);
    }
  }, [open]);

  const handleConfirm = () => {
    if (isValid) {
      onConfirm();
      onOpenChange(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <AlertDialogTitle className="text-red-700">{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-3">
            <p>{description}</p>
            
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="font-medium text-red-800 mb-2">此操作将：</p>
              <ul className="space-y-1 text-sm text-red-700">
                {consequences.map((consequence, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-red-500 mt-0.5">•</span>
                    <span>{consequence}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <p className="text-sm text-yellow-800">
                <strong>⚠️ 重要提醒：</strong>此操作无法撤销，请确保您了解所有后果。
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-input" className="text-sm font-medium">
                请输入 "<span className="font-mono text-red-600">{confirmText}</span>" 来确认：
              </Label>
              <Input
                id="confirm-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={`输入 ${confirmText}`}
                className={`font-mono ${
                  inputValue && !isValid 
                    ? 'border-red-300 focus:border-red-500' 
                    : isValid 
                    ? 'border-green-300 focus:border-green-500' 
                    : ''
                }`}
              />
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!isValid}
            className="bg-red-600 hover:bg-red-700 focus:ring-red-500"
          >
            {actionLabel}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
