'use client';

import * as React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';

interface DangerConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  consequences: string[];
  confirmText: string;
  onConfirm: () => void;
  actionLabel?: string;
  cooldownSeconds?: number;
  showBackupReminder?: boolean;
  showGitHubDisclaimer?: boolean;
}

export function DangerConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  consequences,
  confirmText,
  onConfirm,
  actionLabel = '确认执行',
  cooldownSeconds = 3,
  showBackupReminder = true,
  showGitHubDisclaimer = true
}: DangerConfirmDialogProps) {
  const [inputValue, setInputValue] = React.useState('');
  const [isValid, setIsValid] = React.useState(false);
  const [cooldownRemaining, setCooldownRemaining] = React.useState(0);
  const [isCooldownActive, setIsCooldownActive] = React.useState(false);

  React.useEffect(() => {
    setIsValid(inputValue.toUpperCase() === confirmText.toUpperCase());
  }, [inputValue, confirmText]);

  React.useEffect(() => {
    if (!open) {
      setInputValue('');
      setIsValid(false);
      setCooldownRemaining(0);
      setIsCooldownActive(false);
    }
  }, [open]);

  // 冷却期倒计时
  React.useEffect(() => {
    if (isValid && !isCooldownActive) {
      setIsCooldownActive(true);
      setCooldownRemaining(cooldownSeconds);

      const timer = setInterval(() => {
        setCooldownRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isValid, isCooldownActive, cooldownSeconds]);

  const handleConfirm = () => {
    if (isValid && cooldownRemaining === 0) {
      onConfirm();
      onOpenChange(false);
    }
  };

  const canConfirm = isValid && cooldownRemaining === 0;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-lg">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-destructive" />
            <AlertDialogTitle className="text-base font-medium text-foreground">{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-4 text-sm text-muted-foreground">
            <p>{description}</p>

            <div className="bg-destructive/5 border border-destructive/20 rounded-md p-3">
              <p className="font-medium text-destructive mb-2 text-sm">此操作将：</p>
              <ul className="space-y-1 text-sm text-destructive/80">
                {consequences.map((consequence, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-destructive/60 mt-0.5 text-xs">•</span>
                    <span>{consequence}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 dark:bg-amber-950/20 dark:border-amber-800/30">
              <p className="text-sm text-amber-800 dark:text-amber-200">
                <strong>⚠️ 重要提醒：</strong>此操作无法撤销，请确保您了解所有后果。
              </p>
            </div>

            {showBackupReminder && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 dark:bg-blue-950/20 dark:border-blue-800/30">
                <p className="text-sm text-blue-800 dark:text-blue-200 font-medium mb-2">
                  💾 备份提醒：
                </p>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• 请确保您已备份重要的本地文件</li>
                  <li>• 考虑导出当前的工作进度</li>
                  <li>• 记录重要的配置信息</li>
                </ul>
              </div>
            )}

            {showGitHubDisclaimer && (
              <div className="bg-emerald-50 border border-emerald-200 rounded-md p-3 dark:bg-emerald-950/20 dark:border-emerald-800/30">
                <p className="text-sm text-emerald-800 dark:text-emerald-200">
                  <strong>🌐 GitHub 说明：</strong>此操作仅影响本地数据，
                  <span className="font-medium">不会修改或删除 GitHub.com 上的任何内容</span>。
                  您的远程仓库、提交历史和分支在 GitHub 上保持完全不变。
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="confirm-input" className="text-sm font-medium text-foreground">
                请输入 "<span className="font-mono text-destructive">{confirmText}</span>" 来确认（不区分大小写）：
              </Label>
              <Input
                id="confirm-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={`输入 ${confirmText}（不区分大小写）`}
                className={`font-mono text-sm ${
                  inputValue && !isValid
                    ? 'border-destructive/50 focus:border-destructive'
                    : isValid
                    ? 'border-emerald-500 focus:border-emerald-600'
                    : ''
                }`}
              />
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
          <AlertDialogCancel className="mt-2 sm:mt-0">取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!canConfirm}
            className={`${
              canConfirm
                ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90'
                : 'bg-muted text-muted-foreground cursor-not-allowed'
            } transition-colors duration-200`}
          >
            {cooldownRemaining > 0
              ? `请等待 ${cooldownRemaining} 秒...`
              : actionLabel
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
