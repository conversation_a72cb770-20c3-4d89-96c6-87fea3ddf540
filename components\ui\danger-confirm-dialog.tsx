'use client';

import * as React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertTriangle } from 'lucide-react';

interface DangerConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  consequences: string[];
  confirmText: string;
  onConfirm: () => void;
  actionLabel?: string;
  cooldownSeconds?: number;
  showBackupReminder?: boolean;
  showGitHubDisclaimer?: boolean;
}

export function DangerConfirmDialog({
  open,
  onOpenChange,
  title,
  description,
  consequences,
  confirmText,
  onConfirm,
  actionLabel = '确认执行',
  cooldownSeconds = 3,
  showBackupReminder = true,
  showGitHubDisclaimer = true
}: DangerConfirmDialogProps) {
  const [inputValue, setInputValue] = React.useState('');
  const [isValid, setIsValid] = React.useState(false);
  const [cooldownRemaining, setCooldownRemaining] = React.useState(0);
  const [isCooldownActive, setIsCooldownActive] = React.useState(false);

  React.useEffect(() => {
    setIsValid(inputValue === confirmText);
  }, [inputValue, confirmText]);

  React.useEffect(() => {
    if (!open) {
      setInputValue('');
      setIsValid(false);
      setCooldownRemaining(0);
      setIsCooldownActive(false);
    }
  }, [open]);

  // 冷却期倒计时
  React.useEffect(() => {
    if (isValid && !isCooldownActive) {
      setIsCooldownActive(true);
      setCooldownRemaining(cooldownSeconds);

      const timer = setInterval(() => {
        setCooldownRemaining((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [isValid, isCooldownActive, cooldownSeconds]);

  const handleConfirm = () => {
    if (isValid && cooldownRemaining === 0) {
      onConfirm();
      onOpenChange(false);
    }
  };

  const canConfirm = isValid && cooldownRemaining === 0;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <AlertDialogTitle className="text-red-700">{title}</AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left space-y-3">
            <p>{description}</p>
            
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="font-medium text-red-800 mb-2">此操作将：</p>
              <ul className="space-y-1 text-sm text-red-700">
                {consequences.map((consequence, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-red-500 mt-0.5">•</span>
                    <span>{consequence}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <p className="text-sm text-yellow-800">
                <strong>⚠️ 重要提醒：</strong>此操作无法撤销，请确保您了解所有后果。
              </p>
            </div>

            {showBackupReminder && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <p className="text-sm text-blue-800">
                  <strong>💾 备份提醒：</strong>
                </p>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• 请确保您已备份重要的本地文件</li>
                  <li>• 考虑导出当前的工作进度</li>
                  <li>• 记录重要的配置信息</li>
                </ul>
              </div>
            )}

            {showGitHubDisclaimer && (
              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <p className="text-sm text-green-800">
                  <strong>🌐 GitHub 说明：</strong>此操作仅影响本地数据，
                  <span className="font-semibold">不会修改或删除 GitHub.com 上的任何内容</span>。
                  您的远程仓库、提交历史和分支在 GitHub 上保持完全不变。
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="confirm-input" className="text-sm font-medium">
                请输入 "<span className="font-mono text-red-600">{confirmText}</span>" 来确认：
              </Label>
              <Input
                id="confirm-input"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder={`输入 ${confirmText}`}
                className={`font-mono ${
                  inputValue && !isValid 
                    ? 'border-red-300 focus:border-red-500' 
                    : isValid 
                    ? 'border-green-300 focus:border-green-500' 
                    : ''
                }`}
              />
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={!canConfirm}
            className={`${
              canConfirm
                ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
                : 'bg-gray-400 cursor-not-allowed'
            } transition-colors duration-200`}
          >
            {cooldownRemaining > 0
              ? `请等待 ${cooldownRemaining} 秒...`
              : actionLabel
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
